"use client"

import styles from "./JC_FormTablet.module.scss";
import { useState } from "react";
import J<PERSON><PERSON><PERSON> from "../JC_Field/JC_Field";
import JC_Button from "../JC_Button/JC_Button";
import { JC_FieldModel } from "../../models/ComponentModels/JC_Field";
import { FieldTypeEnum } from "../../enums/FieldType";

export interface JC_FormTabletModel {
    headerLabel: string;
    sections: JC_FormTabletSection[];
    submitButtonText?: string;
    onSubmit?: () => void;
    isLoading?: boolean;
    showSaveButton?: boolean;
}

export interface JC_FormTabletSection {
    Heading: string;
    Fields: JC_FieldModel[];
}

interface JC_FormTabletProps {
    model: JC_FormTabletModel;
}

export default function JC_FormTablet({ model }: JC_FormTabletProps) {
    const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);

    // Create tile structure from sections and fields
    const getTileStructure = () => {
        const tiles: Array<{
            id: string;
            label: string;
            isHeading: boolean;
            field?: JC_FieldModel;
        }> = [];

        model.sections.forEach((section, sectionIndex) => {
            // Add section heading
            tiles.push({
                id: `section-${sectionIndex}`,
                label: section.Heading,
                isHeading: true
            });

            // Add fields for this section
            section.Fields.forEach(field => {
                tiles.push({
                    id: field.inputId,
                    label: field.label || field.inputId,
                    isHeading: false,
                    field: field
                });
            });
        });

        return tiles;
    };

    const tileStructure = getTileStructure();

    // Handle tile selection
    const handleTileClick = (tileId: string) => {
        const tile = tileStructure.find(t => t.id === tileId);
        if (tile && !tile.isHeading) {
            setSelectedFieldId(tileId);
        }
    };

    // Get selected tile
    const selectedTile = tileStructure.find(t => t.id === selectedFieldId);
    const selectedField = selectedTile?.field;

    // Handle field value changes
    const handleFieldChange = (newValue: string) => {
        if (selectedField && selectedField.onChange) {
            selectedField.onChange(newValue);
        }
    };

    // Handle form submission
    const handleSubmit = () => {
        if (model.onSubmit) {
            model.onSubmit();
        }
    };

    return (
        <div className={styles.mainContainer}>
            {/* Header */}
            <div className={styles.header}>
                <h2 className={styles.headerLabel}>{model.headerLabel}</h2>
            </div>

            {/* Content Area */}
            <div className={styles.contentArea}>
                {/* Left Pane - Field Tiles */}
                <div className={styles.leftPane}>
                    <div className={styles.tileContainer}>
                        {tileStructure.map((tile) => (
                            <div
                                key={tile.id}
                                className={`${styles.tile} ${
                                    tile.isHeading
                                        ? styles.headingTile
                                        : `${styles.fieldTile} ${selectedFieldId === tile.id ? styles.selectedTile : ''}`
                                }`}
                                onClick={() => handleTileClick(tile.id)}
                            >
                                {tile.isHeading ? (
                                    tile.label
                                ) : (
                                    <>
                                        <div className={styles.fieldLabel}>{tile.label}</div>
                                        <div className={styles.fieldValue}>
                                            {tile.field?.value || "-"}
                                        </div>
                                    </>
                                )}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Pane - Field Editor */}
                <div className={styles.rightPane}>
                    {selectedField ? (
                        <div className={styles.fieldEditor}>
                            {selectedField.type === FieldTypeEnum.Dropdown && selectedField.options ? (
                                <div className={styles.optionsList}>
                                    {selectedField.options.map((option) => (
                                        <div
                                            key={option.OptionId}
                                            className={`${styles.optionTile} ${
                                                selectedField.value === option.OptionId
                                                    ? styles.selectedOption
                                                    : ''
                                            }`}
                                            onClick={() => handleFieldChange(option.OptionId)}
                                        >
                                            {option.Label}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <JC_Field
                                    inputId={selectedField.inputId}
                                    type={selectedField.type}
                                    label=""
                                    value={selectedField.value || ""}
                                    placeholder={selectedField.placeholder}
                                    readOnly={selectedField.readOnly}
                                    onChange={handleFieldChange}
                                    onBlur={selectedField.onBlur}
                                    onEnter={selectedField.onEnter}
                                    onEscape={selectedField.onEscape}
                                    validate={selectedField.validate}
                                    required={selectedField.required}
                                />
                            )}
                        </div>
                    ) : (
                        <div className={styles.emptyState}>
                            Select a field to edit
                        </div>
                    )}
                </div>
            </div>

            {/* Footer */}
            {model.showSaveButton !== false && (
                <div className={styles.footer}>
                    <JC_Button
                        text={model.submitButtonText || "Save"}
                        onClick={handleSubmit}
                        isLoading={model.isLoading || false}
                    />
                </div>
            )}
        </div>
    );
}
